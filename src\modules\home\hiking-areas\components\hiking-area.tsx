"use client";

import React from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { UseGetHikingArea } from "../queries/use-get-hiking-area";

const HikingAreasDisplay: React.FC = () => {
  const { data, isLoading, error } = UseGetHikingArea();
  const router = useRouter();

  if (isLoading) return <p>Loading hiking areas...</p>;
  if (error) return <p>Error loading hiking areas: {error.message}</p>;

  const hikingAreaBlock = data?.data;

  return (
    <div className="p-6 container mx-auto text-center max-w-6xl">
      {hikingAreaBlock ? (
        <>
          <h1 className="text-3xl font-bold mb-4">{hikingAreaBlock.heading}</h1>
          {hikingAreaBlock.subHeading && (
            <p className="mb-6">{hikingAreaBlock.subHeading}</p>
          )}

          {hikingAreaBlock.areas.length > 0 ? (
            <table className="min-w-full border border-gray-300 mb-6">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border px-4 py-2">Image</th>
                  <th className="border px-4 py-2">Title</th>
                  <th className="border px-4 py-2">Subtitle</th>
                  <th className="border px-4 py-2">Link</th>
                </tr>
              </thead>
              <tbody>
                {hikingAreaBlock.areas.map((area) => (
                  <tr key={area.id} className="even:bg-gray-50">
                    <td className="border px-4 py-2">
                      {area.image && !area.image.includes("example.com") ? (
                        <Image
                          src={area.image}
                          alt={area.title}
                          width={100}
                          height={60}
                          className="rounded object-cover"
                        />
                      ) : (
                        <Image
                          src="/images/placeholder.jpg"
                          alt="placeholder"
                          width={100}
                          height={60}
                        />
                      )}
                    </td>
                    <td className="border px-4 py-2">{area.title}</td>
                    <td className="border px-4 py-2">{area.subtitle}</td>
                    <td className="border px-4 py-2">
                      <a
                        href={area.linkUrl}
                        target="_blank"
                        rel="noreferrer"
                        className="text-blue-600 hover:underline"
                      >
                        {area.linkUrl}
                      </a>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <p className="mb-6">No hiking areas found.</p>
          )}

          <button
            className="bg-brand text-white px-4 py-2 rounded hover:bg-brand/80"
            onClick={() => router.push("/hiking-areas/edit")}
          >
            Edit Hiking Areas
          </button>
        </>
      ) : (
        <>
          <p className="mb-6">No hiking area data found.</p>
          <button
            className="bg-brand text-white px-4 py-2 rounded hover:bg-brand/80"
            onClick={() => router.push("/hiking-areas/create")}
          >
            Create Hiking Area
          </button>
        </>
      )}
    </div>
  );
};

export default HikingAreasDisplay;
