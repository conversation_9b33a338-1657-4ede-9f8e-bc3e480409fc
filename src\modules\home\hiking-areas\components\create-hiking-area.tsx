"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { IHikingArea, IHikingAreaItem } from "@/types/home";
import { toast } from "sonner";
import { UseCreateHikingArea } from "../mutations/use-create-hiking-area";
import { useGetHome } from "../../queries/get-home";

const CreateHikingAreaPage: React.FC = () => {
    const router = useRouter();
    const createHikingArea = UseCreateHikingArea();
    const { data: homeData, isLoading: homeLoading, error: homeError } = useGetHome();

    const [heading, setHeading] = useState("");
    const [subHeading, setSubHeading] = useState("");
    const [areas, setAreas] = useState<IHikingAreaItem[]>([]);

    const [newArea, setNewArea] = useState<IHikingAreaItem>({
        id: "",
        homeHikingId: "",
        title: "",
        subtitle: "",
        image: "",
        linkUrl: "",
        createdAt: "",
        updatedAt: "",
    });

    if (homeLoading) return <p>Loading home data...</p>;
    if (homeError) return <p>Error loading home data: {homeError.message}</p>;

    const homeId = homeData?.data?.id ?? "";

    const handleAddArea = () => {
        if (!newArea.title.trim()) {
            toast.error("Area title is required");
            return;
        }
        setAreas((prev) => [
            ...prev,
            {
                ...newArea,
                id: crypto.randomUUID(),
                homeHikingId: homeId,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
            },
        ]);
        setNewArea({
            id: "",
            homeHikingId: "",
            title: "",
            subtitle: "",
            image: "",
            linkUrl: "",
            createdAt: "",
            updatedAt: "",
        });
    };

    const handleFeatureChange = (
        field: keyof IHikingAreaItem,
        value: string
    ) => {
        setNewArea((prev) => ({ ...prev, [field]: value }));
    };

    const handleDeleteArea = (index: number) => {
        setAreas((prev) => prev.filter((_, i) => i !== index));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!homeId) {
            toast.error("Home ID is required");
            return;
        }
        if (areas.length === 0) {
            toast.error("Please add at least one hiking area");
            return;
        }
        const payload: IHikingArea = {
            id: crypto.randomUUID(),
            heading,
            subHeading,
            homeId,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            areas,
        };
        createHikingArea.mutate(payload, {
            onSuccess: () => {
                toast.success("Hiking Area created successfully");
                router.push("/hiking-areas");
            },
            onError: () => {
                toast.error("Failed to create hiking area");
            },
        });
    };

    return (
        <div className="container mx-auto p-6 space-y-6 max-w-3xl">
            <h1 className="text-3xl font-bold">Create Hiking Area</h1>

            <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                    <label className="block mb-1 font-medium">Heading</label>
                    <Input
                        value={heading}
                        onChange={(e) => setHeading(e.target.value)}
                        placeholder="Enter main heading"
                        required
                    />
                </div>

                <div>
                    <label className="block mb-1 font-medium">Subheading</label>
                    <Textarea
                        value={subHeading}
                        onChange={(e) => setSubHeading(e.target.value)}
                        placeholder="Enter subheading"
                    />
                </div>

                <div>
                    <h2 className="text-xl font-semibold mb-4">Hiking Areas</h2>
                    {areas.length > 0 &&
                        areas.map((area, index) => (
                            <div
                                key={area.id}
                                className="mb-4 border p-4 rounded bg-white shadow-sm"
                            >
                                <div className="flex justify-between items-center mb-2">
                                    <h3 className="font-semibold">{area.title || "(No Title)"}</h3>
                                    <Button
                                        variant="destructive"
                                        size="sm"
                                        onClick={() => handleDeleteArea(index)}
                                    >
                                        Delete
                                    </Button>
                                </div>
                                <p className="mb-1 text-gray-700">{area.subtitle}</p>
                                <p className="text-sm text-blue-600 hover:underline">
                                    <a href={area.linkUrl} target="_blank" rel="noreferrer">
                                        {area.linkUrl}
                                    </a>
                                </p>
                            </div>
                        ))}

                    <div className="border p-4 rounded space-y-4 bg-gray-50">
                        <Input
                            placeholder="Title"
                            value={newArea.title}
                            onChange={(e) => handleFeatureChange("title", e.target.value)}
                            required
                        />
                        <Input
                            placeholder="Subtitle"
                            value={newArea.subtitle}
                            onChange={(e) => handleFeatureChange("subtitle", e.target.value)}
                        />
                        <Input
                            placeholder="Image URL"
                            value={newArea.image}
                            onChange={(e) => handleFeatureChange("image", e.target.value)}
                        />
                        <Input
                            placeholder="Link URL"
                            value={newArea.linkUrl}
                            onChange={(e) => handleFeatureChange("linkUrl", e.target.value)}
                        />
                        <Button type="button" onClick={handleAddArea}>
                            + Add Hiking Area
                        </Button>
                    </div>
                </div>

                <div className="flex justify-end space-x-3">
                    <Button
                        type="button"
                        variant="outline"
                        onClick={() => router.push("/hiking-areas")}
                    >
                        Cancel
                    </Button>
                    <Button type="submit">
                        Save
                    </Button>
                </div>
            </form>
        </div>
    );
};

export default CreateHikingAreaPage;
